# ninja log v6
45	435	7770598875435737	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	e7d977418de16b3
108	446	7770598876063950	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	88908eb5fb33ce7a
37	455	7770598875355726	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	aca003aa4566b13a
68	469	7770598875657695	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	ac7edd2f1fa809d8
145	504	7770598876430318	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	1f718452fc212f5d
119	515	7770598876169112	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	b23d6c9fab4fb907
91	524	7770598875897798	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	743ba3fd0c911355
27	534	7770598875255716	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	4f0629051380126c
101	544	7770598875993919	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	7c89f4ca13349123
52	552	7770598875505715	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	5a7eac5c1c09cc87
126	571	7770598876239125	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	2b3bd89de35630e5
136	586	7770598876339112	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	e45e10918050dfb0
75	607	7770598875727700	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	2e83016f7125c2f6
83	615	7770598875807810	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	a4a011e2d9816f80
192	625	7770598876906004	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	4b11df6725e9d1ba
154	633	7770598876521293	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	ae5cfc05c5a8fd10
60	652	7770598875585728	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	89f36d9401723be
174	844	7770598876721856	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	f6662c89472fb1e
437	887	7770598879359642	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	af97d10c98fd6215
446	938	7770598879449645	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	47c208067be9d651
552	1002	7770598880510851	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	a8bd9c382a66122e
544	1021	7770598880420838	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	e84b56dfae6d94b6
572	1030	7770598880710835	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	c3ad8b2ae0f2f761
516	1069	7770598880145659	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	c964cf0e34d93029
470	1078	7770598879679643	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	f275cb54fd229b45
535	1087	7770598880330861	esp-idf/log/liblog.a	9fe540e68b3f479f
608	1097	7770598881056070	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	2a42468e9b6967fa
525	1140	7770598880230852	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	fc84eedbea8e4208
587	1173	7770598880850926	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	16cdbc0577b5304
634	1182	7770598881322383	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	a51da225f72d8a59
616	1194	7770598881146147	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	4ea0fc324b815d8
625	1209	7770598881237216	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	10c2ae2c52d4280f
505	1243	7770598880025590	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	f4576ebb998764d7
456	1284	7770598879539644	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	62b9078695bdf7a9
652	1390	7770598881502368	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	80620778d920c90f
1022	1438	7770598885198701	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	693b1cefdf5675af
844	1502	7770598883425943	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	2e1da854db8e92a3
1030	1514	7770598885288808	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	436ab8398ae337cd
1003	1551	7770598885008714	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	7d9848c03906a592
1140	1561	7770598886393038	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	c572c5c7d1787c64
1087	1577	7770598885853669	esp-idf/esp_rom/libesp_rom.a	76a5ded7b2ca549c
1070	1587	7770598885687970	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	b3d5472d26ef9a3
888	1595	7770598883856837	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	6fc297156240c2d6
1097	1605	7770598885958738	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	548cd74f6dddaa83
1079	1645	7770598885769901	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	d4c198ef93cb0627
939	1654	7770598884366821	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	3c045548febdeacf
1243	1663	7770598887415770	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	958a6f0343d3a6ae
1182	1723	7770598886803078	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	dd68be7af830e54b
1438	1746	7770598889363959	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	fb1d783f1fc6999f
1194	1764	7770598886927123	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	8691aaacde663ef1
1605	1887	7770598891038253	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	f088d255c3206d2d
1577	1899	7770598890757576	esp-idf/esp_common/libesp_common.a	16d17467a2c4e525
1561	1913	7770598890597596	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	531bac33cf71ed00
1551	1921	7770598890492440	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	7be12f9a9c15e345
1173	1930	7770598886713074	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	73bad240e72038db
1515	1941	7770598890132377	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	443b96efb7c6069c
1390	1951	7770598888888719	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	28b9d620aa85dde0
1503	1961	7770598890012369	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	8c5fa4f5a3433383
1655	1971	7770598891528161	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	56faded760bb5c6d
1664	1986	7770598891618144	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	4d7101eb17ce0b4e
1596	2001	7770598890939086	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	d45cd5d41e6f2780
1724	2061	7770598892217808	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	3218a7a712f7c9f3
1646	2090	7770598891442961	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	e6322ded129131d8
1746	2123	7770598892451997	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	dc5ff90409846236
1285	2144	7770598887833207	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	c8e18c3ff045ec9e
1931	2158	7770598894291275	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	12889012e50b6dec
1209	2169	7770598887072276	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	61276d89f376900b
1921	2178	7770598894199364	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	4ece997a782feb60
1942	2186	7770598894399785	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	8117b1abdd8ae5ad
2002	2213	7770598895004343	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	b57f8c2a254bd570
1971	2223	7770598894695255	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	55e4485b45905110
1961	2231	7770598894595266	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	f9302f071792c865
1765	2239	7770598892627160	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	514081a2fae92ff2
1587	2248	7770598890849098	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	18fb62ac85589c67
1986	2256	7770598894845329	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	391f82e2f04a00c5
1952	2264	7770598894510085	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	4c46a4c58ed3afe3
1899	2272	7770598893970730	esp-idf/esp_hw_support/libesp_hw_support.a	a22e20a51d2bd2b0
1888	2299	7770598893853159	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	977179509d5045cf
2061	2325	7770598895591117	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	728e213496b12ab9
2159	2334	7770598896571579	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	c6232ff47fd254ba
2090	2345	7770598895881200	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	8b1a3e9c13088575
2123	2353	7770598896216345	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	36284a70d4d93c65
2145	2370	7770598896436429	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	1d6d4d64b28f05f5
2170	2395	7770598896681597	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	79a690756593a85d
2178	2404	7770598896761570	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	622b247c7ed7b2f3
2187	2406	7770598896843965	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	d68e5dc1f90372f1
2223	2411	7770598897218248	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	cd88ef30ced30729
2256	2417	7770598897549563	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	1e186cd9fac3c7b3
2214	2439	7770598897114016	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	2f7eee66471fe57
2264	2451	7770598897629625	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	c54abca9198becba
2231	2458	7770598897293886	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	217868e2020e27fc
2370	2466	7770598899496999	project_elf_src_esp32s3.c	b8d0ff71e4b9b057
2370	2466	7770598899496999	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/project_elf_src_esp32s3.c	b8d0ff71e4b9b057
2239	2473	7770598897378613	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	f08f3d5dee8b8c0
2249	2486	7770598897469600	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	88fd07eda1bf1b0d
2300	2515	7770598897978491	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	2383bf602ff145a4
2325	2516	7770598898228483	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	f9f82b55bf0e4872
2346	2519	7770598898438554	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	cd9fe949a67de97
2272	2531	7770598897709632	esp-idf/esp_system/libesp_system.a	6ba3db5ebc7222d0
2335	2564	7770598898328497	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	1c61d55442d51011
2466	2579	7770598899644742	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	f6defcaf21da4b68
2353	2579	7770598898521169	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	8fd2f941b1abe2d7
2395	2626	7770598898937813	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	834482eb125c9057
2531	2687	7770598900293054	esp-idf/efuse/libefuse.a	b027f75ac9dc6d3a
1913	2801	7770598894116933	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	75d15f451b03cdd4
2687	2863	7770598901848620	esp-idf/bootloader_support/libbootloader_support.a	41abbb3847180a9a
2863	2985	7770598903609914	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	7704240e3fc65260
2985	3099	7770598904833936	esp-idf/spi_flash/libspi_flash.a	f2f1321b4133f01b
3099	3226	7770598905965659	esp-idf/hal/libhal.a	4ecd0b7d65881b5c
3227	3345	7770598907247539	esp-idf/micro-ecc/libmicro-ecc.a	564c426ed7c13bba
3345	3533	7770598908436244	esp-idf/soc/libsoc.a	be819c31531e0d15
3533	3667	7770598910310856	esp-idf/xtensa/libxtensa.a	1e98b69c813b3b67
3667	3795	7770598911659665	esp-idf/main/libmain.a	5d9eeaf9cc3081fe
3795	4016	7770598912922965	bootloader.elf	7788bcbde2f2077
4016	4300	7770598917928176	.bin_timestamp	e8ef78346f87dfbd
4016	4300	7770598917928176	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/.bin_timestamp	e8ef78346f87dfbd
4301	4393	7770598917990495	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
4301	4393	7770598917990495	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
