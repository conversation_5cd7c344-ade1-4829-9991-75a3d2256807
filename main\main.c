/*
 * SPDX-FileCopyrightText: 2010-2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_timer.h"
#include "../components/LVGL/src/lvgl.h"
#include "../components/LVGL/demos/music/lv_demo_music.h"
#include "lv_port/lv_port_disp.h"
#include "lv_port/lv_port_indev.h"

static esp_timer_handle_t lvgl_tick_timer = NULL;

// 定时回调函数，每 1ms 触发
static void lv_tick_task(void *arg) {
    lv_tick_inc(1);
}

// 初始化 LVGL Tick 定时器
void lvgl_tick_timer_init(void) {
    const esp_timer_create_args_t timer_args = {
        .callback = &lv_tick_task,
        .arg = NULL,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "lv_tick_timer"
    };

    esp_timer_create(&timer_args, &lvgl_tick_timer);
    esp_timer_start_periodic(lvgl_tick_timer, 1000); // 1ms 触发
}

void app_main(void)
{
    /* 初始化LVGL时基定时器 */
    lvgl_tick_timer_init();

    /* 初始化LVGL */
    lv_init();

    /* 初始化显示驱动 */
    lv_port_disp_init();

    /* 初始化输入设备驱动 */
    lv_port_indev_init();

    /* 创建一个简单的测试标签 */
    lv_obj_t * label = lv_label_create(lv_scr_act());
    lv_label_set_text(label, "LVGL Display Test\nHello World!");
    lv_obj_center(label);

    /* 启动音乐播放器演示 */
    // lv_demo_music();

    /* 主循环 */
    while (1) {
        lv_task_handler();  // LVGL 任务管理
        vTaskDelay(pdMS_TO_TICKS(10));  // 延迟 10ms
    }
}
